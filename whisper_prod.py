from faster_whisper import WhisperModel, BatchedInferencePipeline
from flask import Flask, request, jsonify
import os
import logging
import threading
from collections import deque

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

app = Flask(__name__)

logger.info("Carregando modelos...")
model_instances = [
    BatchedInferencePipeline(model=WhisperModel("large-v3", device="cuda", compute_type="float16")),
    BatchedInferencePipeline(model=WhisperModel("large-v3", device="cuda", compute_type="float16")),
    BatchedInferencePipeline(model=WhisperModel("large-v3", device="cuda", compute_type="float16"))
]
logger.info("Modelos carregados com sucesso.")

model_states = [False, False, False]  # False indica que a instância está livre
model_lock = threading.Lock()
reservation_queue = deque()

def reserve_model():
    global model_states
    reservation_event = threading.Event()
    with model_lock:
        for i, state in enumerate(model_states):
            if not state:
                model_states[i] = True
                return i
        reservation_queue.append(reservation_event)
    reservation_event.wait()  # Espera até que um modelo esteja disponível
    return reserve_model()  # Tenta novamente para pegar um modelo agora que a fila está liberada

def release_model(index):
    global model_states
    with model_lock:
        model_states[index] = False
        if reservation_queue:
            reservation_event = reservation_queue.popleft()
            reservation_event.set()  # Avisa a próxima requisição na fila

def transcribe_audio(file_path):
    model_index = reserve_model()
    if model_index is None:
        return None, "No available model instances."

    try:
        model = model_instances[model_index]
        segments, info = model.transcribe(file_path, batch_size=16, language='pt')
        text = "".join([segment.text for segment in segments])
        return text, info.language, info.language_probability
    finally:
        release_model(model_index)

@app.route('/transcribe', methods=['POST'])
def transcribe_endpoint():
    data = request.get_json()
    file_path = data.get("file_path")

    if not file_path or not os.path.isfile(file_path):
        return jsonify({"error": "Invalid file path"}), 400

    try:
        text, language, language_probability = transcribe_audio(file_path)

        if text is None:
            return jsonify({"error": "Failed to transcribe"}), 503

        return jsonify({
            "text": text,
            "language": language,
            "language_probability": language_probability
        })
    except Exception as e:
        logger.error(f"Exception: {traceback.format_exc()}")
        return jsonify({"error": "An unexpected error occurred."}), 500


