start_time=$(date +%s)

find /home/<USER>/audios/small/files/008e5d51-7916-4008-9eef-ab63b7c0b6d8 -type f \( -iname "*.mp3" -o -iname "*.wav" -o -iname "*.oga" \) -print0 | \
xargs -0 stat -c "%s %n" | \
sort -n | \
head -n 50 | \
cut -d' ' -f2- | \
xargs -n1 -P15 -I{} sh -c 'start=$(date +%s); curl -X POST -F "audio=@{}" https://transcriber-aws.ikatec.cloud/transcribe; end=$(date +%s); echo "Request to {} took $((end - start)) seconds.\n\r"'

end_time=$(date +%s)
total_time=$((end_time - start_time))
echo "Total time taken: $total_time seconds"
