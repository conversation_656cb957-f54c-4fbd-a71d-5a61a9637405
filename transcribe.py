from flask import Flask, request, jsonify
import requests
import os
import logging

app = Flask(__name__)

# Configuração de logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Carregar variáveis de ambiente
WHISPER_SERVICE_URL = os.getenv("WHISPER_SERVICE_URL", "http://whisper:11434/transcribe")
TEMP_DIR = "/app/data"

@app.errorhandler(500)
def handle_internal_server_error(e):
    logger.error(f"Internal Server Error: {str(e)}")
    response = jsonify({"error": "Internal Server Error", "message": str(e)})
    response.status_code = 500
    return response

@app.errorhandler(Exception)
def handle_unexpected_error(e):
    logger.error(f"Unexpected Error: {str(e)}")
    response = jsonify({"error": "Unexpected Error", "message": str(e)})
    response.status_code = 500
    return response

@app.route('/transcribe', methods=['POST'])
def transcribe_endpoint():

    if 'audio' not in request.files:
        logger.debug("No audio part in request.files")
        return jsonify({"error": "No audio file provided"}), 400

    audio_file = request.files['audio']
    
    file_path = os.path.join(TEMP_DIR, audio_file.filename)
    audio_file.save(file_path)
    file_name = os.path.basename(file_path)  # Extrair o nome do arquivo

    try:
        # Enviar o caminho do arquivo para o serviço de transcrição
        response = requests.post(
            WHISPER_SERVICE_URL,
            json={"file_path": file_path}
        )

        if response.ok:
            return response.json()
        else:
            logger.error(f"Failed to transcribe: {response.text}")
            return jsonify({"error": "Failed to transcribe", "file_name": file_name}), response.status_code
    except requests.exceptions.RequestException as e:
        logger.error(f"RequestException: {str(e)}")
        return jsonify({"error": "Failed to transcribe", "message": str(e), "file_name": file_name}), 503
    except Exception as e:
        logger.error(f"Exception: {str(e)}")
        return jsonify({"error": "Unexpected Error", "message": str(e), "file_name": file_name}), 500
    finally:
        try:
            os.remove(file_path)
        except Exception as e:
            logger.error(f"Failed to remove file: {file_path}. Error: {str(e)}")


