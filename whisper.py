from faster_whisper import WhisperModel, BatchedInferencePipeline
from flask import Flask, request, jsonify
import os
import logging
import threading
from collections import deque
import traceback
import time

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

app = Flask(__name__)


def load_model_sequentially(model_name, device, compute_type):
    models = []
    for _ in range(5):  # Tentando carregar 5 modelos
        model = WhisperModel(model_name, device=device, compute_type=compute_type)
        pipeline = BatchedInferencePipeline(model=model)
        models.append(pipeline)
        logger.info(f"Modelo carregado: {model_name} - .")
        time.sleep(10)  # Espera para garantir que o modelo esteja completamente carregado
    return models

logger.info("Carregando modelos...")
# model_instances = [
#     BatchedInferencePipeline(model=WhisperModel("large-v3", device="cuda", compute_type="float16")),
#     BatchedInferencePipeline(model=WhisperModel("large-v3", device="cuda", compute_type="float16")),
#     BatchedInferencePipeline(model=WhisperModel("large-v3", device="cuda", compute_type="float16"))
# ]

model_instances = load_model_sequentially("large-v3", "cuda", "float16")

logger.info("Modelos carregados com sucesso.")

model_states = [False, False, False]  # False indica que a instância está livre
model_lock = threading.Lock()
reservation_queue = deque()

def reserve_model():
    global model_states
    reservation_event = threading.Event()
    with model_lock:
        for i, state in enumerate(model_states):
            if not state:
                model_states[i] = True
                logger.info(f"Modelo {i} reservado.")
                return i
        reservation_queue.append(reservation_event)
    reservation_event.wait()  # Espera até que um modelo esteja disponível
    return reserve_model()  # Tenta novamente para pegar um modelo agora que a fila está liberada

def release_model(index):
    global model_states
    with model_lock:
        model_states[index] = False
        logger.info(f"Modelo {index} liberado.")
        if reservation_queue:
            reservation_event = reservation_queue.popleft()
            reservation_event.set()  # Avisa a próxima requisição na fila

def transcribe_audio(file_path):
    max_attempts = 3
    attempt = 0

    while attempt < max_attempts:
        model_index = reserve_model()
        if model_index is None:
            logger.error("Nenhum modelo disponível.")
            return None  # Retorna apenas texto

        try:
            logger.info(f"Transcrevendo o arquivo {file_path} com o modelo {model_index}.")
            model = model_instances[model_index]
            segments, info = model.transcribe(file_path, batch_size=16, language='pt')  # Defina o idioma aqui
            text = "".join([segment.text for segment in segments])
            logger.info(f"Transcrição concluída para o arquivo {file_path}.")
            return text
        except Exception as e:
            logger.error(f"Falha na transcrição: {e}. Tentativa {attempt + 1}/{max_attempts}.")
            attempt += 1
            time.sleep(2)  # Delay de 2 segundos antes de tentar novamente
        finally:
            release_model(model_index)
    
    logger.error(f"Falha na transcrição após {max_attempts} tentativas.")
    return None  # Retorna apenas texto

@app.route('/transcribe', methods=['POST'])
def transcribe_endpoint():
    data = request.get_json()
    file_path = data.get("file_path")

    if not file_path or not os.path.isfile(file_path):
        return jsonify({"error": "Invalid file path"}), 400

    try:
        logger.info(f"Iniciando transcrição para o arquivo {file_path}.")
        text = transcribe_audio(file_path)  # Recebe apenas o texto

        if text is None:
            return jsonify({
                "error": "Failed to transcribe",
                "file_path": file_path
            }), 503

        return jsonify({
            "text": text
        })
    except Exception as e:
        logger.error(f"Exception: {traceback.format_exc()}")
        return jsonify({
            "error": "An unexpected error occurred.",
            "file_path": file_path
        }), 500

