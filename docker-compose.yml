services:
  whisper:
    build:
      context: .
      dockerfile: Dockerfile.whisper
    ports:
      - "11434:11434"
    volumes:
      - shared-data:/app/data
    restart: unless-stopped
    environment:
      - NVIDIA_VISIBLE_DEVICES=all
      - NVIDIA_DRIVER_CAPABILITIES=compute,utility
    deploy:
      resources:
        reservations:
          devices:
            - driver: nvidia
              count: all
              capabilities: [gpu]
    runtime: nvidia

  transcribe:
    build:
      context: .
      dockerfile: Dockerfile.transcribe
    volumes:
      - shared-data:/app/data
    ports:
      - "8080:8080"
    environment:
      - GUNICORN_WORKERS=${GUNICORN_WORKERS}
      - GUNICORN_THREADS=${GUNICORN_THREADS}

  nginx:
    image: nginx:latest
    ports:
      - "80:80"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf:ro
    depends_on:
      - transcribe

volumes:
  shared-data:

