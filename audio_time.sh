#!/bin/bash

# Caminho para a pasta com os arquivos de áudio
AUDIO_DIR="/home/<USER>/audios/small/files/008e5d51-7916-4008-9eef-ab63b7c0b6d8/"

# Inicializa a duração total em segundos
total_seconds=0

# Encontra todos os arquivos de áudio e itera sobre eles
find "$AUDIO_DIR" -type f \( -iname "*.mp3" -o -iname "*.wav" -o -iname "*.oga" \) | while read -r file; do
  echo "Processing file: $file" # Mensagem de depuração
  # Obtém a duração do arquivo usando ffprobe
  duration=$(ffprobe -v error -show_entries format=duration -of default=noprint_wrappers=1:nokey=1 "$file" 2>/dev/null)
  
  # Verifica se a duração é válida
  if [ -z "$duration" ] || ! [[ "$duration" =~ ^[0-9]+(\.[0-9]+)?$ ]]; then
    echo "Invalid duration for file: $file or duration not found"
    continue
  fi

  echo "Duration for $file: $duration seconds" # Mensagem de depuração
  
  # Adiciona a duração ao total
  total_seconds=$(echo "$total_seconds + $duration" | bc)
  
  # Mensagem de depuração para verificação do total acumulado
  echo "Accumulated total seconds: $total_seconds"
done

# Mostra o total em segundos para depuração
echo "Total seconds: $total_seconds"

# Converte o tempo total para horas, minutos e segundos
hours=$(echo "$total_seconds / 3600" | bc)
minutes=$(echo "($total_seconds % 3600) / 60" | bc)
seconds=$(echo "$total_seconds % 60" | bc)

# Exibe o tempo total
echo "Total time: ${hours}h ${minutes}m ${seconds}s"
