# Implementação das Otimizações para Alta Performance

## Resumo Executivo

**Problema Atual**: Sistema processa apenas 5 transcrições simultâneas, subutilizando GPU A10G (24GB)

**Solução Proposta**: Migração para arquitetura assíncrona com otimizações de modelo

**Resultado Esperado**: 3-5x aumento no throughput (300-500 arquivos/hora)

## Implementação Fase 1: Otimizações Imediatas

### 1. Novo whisper_optimized.py

```python
from faster_whisper import WhisperModel, BatchedInferencePipeline
from fastapi import FastAPI, UploadFile, HTTPException
import asyncio
import os
import logging
from concurrent.futures import ThreadPoolExecutor
from typing import List
import uvicorn

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

app = FastAPI(title="Optimized Whisper Service")

class OptimizedModelPool:
    def __init__(self, model_size="medium", compute_type="int8", max_models=12):
        self.model_size = model_size
        self.compute_type = compute_type
        self.max_models = max_models
        self.models = []
        self.available = asyncio.Queue()
        self.executor = ThreadPoolExecutor(max_workers=max_models)
        
    async def initialize(self):
        """Carrega modelos de forma otimizada"""
        logger.info(f"Carregando {self.max_models} modelos {self.model_size} com {self.compute_type}")
        
        for i in range(self.max_models):
            model = WhisperModel(
                self.model_size, 
                device="cuda", 
                compute_type=self.compute_type,
                # Otimizações específicas para A10G
                device_index=0,
                cpu_threads=2
            )
            pipeline = BatchedInferencePipeline(model=model)
            self.models.append(pipeline)
            await self.available.put(pipeline)
            logger.info(f"Modelo {i+1}/{self.max_models} carregado")
    
    async def get_model(self):
        return await self.available.get()
    
    async def return_model(self, model):
        await self.available.put(model)
    
    def transcribe_sync(self, model, file_path, batch_size=8):
        """Função síncrona para execução em thread"""
        try:
            segments, info = model.transcribe(
                file_path, 
                batch_size=batch_size, 
                language='pt',
                # Otimizações adicionais
                vad_filter=True,
                vad_parameters=dict(min_silence_duration_ms=500)
            )
            text = "".join([segment.text for segment in segments])
            return text
        except Exception as e:
            logger.error(f"Erro na transcrição: {e}")
            raise

# Pool global de modelos
model_pool = OptimizedModelPool()

@app.on_event("startup")
async def startup_event():
    await model_pool.initialize()

@app.post("/transcribe")
async def transcribe_endpoint(file_path: str):
    if not os.path.isfile(file_path):
        raise HTTPException(status_code=400, detail="Invalid file path")
    
    model = await model_pool.get_model()
    try:
        loop = asyncio.get_event_loop()
        text = await loop.run_in_executor(
            model_pool.executor,
            model_pool.transcribe_sync,
            model,
            file_path
        )
        return {"text": text}
    finally:
        await model_pool.return_model(model)

@app.get("/health")
async def health_check():
    return {
        "status": "healthy",
        "available_models": model_pool.available.qsize(),
        "total_models": len(model_pool.models)
    }

if __name__ == "__main__":
    uvicorn.run(app, host="0.0.0.0", port=11434, workers=1)
```

### 2. Dockerfile.whisper.optimized

```dockerfile
FROM nvidia/cuda:12.5.1-cudnn-devel-ubuntu22.04

WORKDIR /app

# Otimizações de sistema
RUN apt-get update && apt-get install -y \
    libsndfile1 \
    ffmpeg \
    git \
    python3 \
    libcudnn8 \
    libcudnn8-dev \
    python3-pip \
    htop \
    nvidia-utils-535 \
    && rm -rf /var/lib/apt/lists/*

# Otimizações Python
RUN python3 -m pip install --upgrade pip
RUN pip install torch torchvision torchaudio --index-url https://download.pytorch.org/whl/cu124

# Instalar dependências otimizadas
RUN pip install nvidia-pyindex nvidia-cudnn
RUN pip install --force-reinstall "faster-whisper @ https://github.com/SYSTRAN/faster-whisper/archive/refs/heads/master.tar.gz"
RUN pip install fastapi uvicorn[standard] python-multipart

COPY whisper_optimized.py /app/whisper_optimized.py
COPY requirements_optimized.txt /app/requirements.txt

RUN pip install -r requirements.txt

# Pré-download do modelo para cache
RUN python3 -c "from faster_whisper import WhisperModel; WhisperModel('medium', device='cpu')"

EXPOSE 11434

# Configurações otimizadas para A10G
ENV CUDA_VISIBLE_DEVICES=0
ENV NVIDIA_VISIBLE_DEVICES=all
ENV NVIDIA_DRIVER_CAPABILITIES=compute,utility

CMD ["uvicorn", "whisper_optimized:app", "--host", "0.0.0.0", "--port", "11434", "--workers", "1"]
```

### 3. requirements_optimized.txt

```
fastapi>=0.104.0
uvicorn[standard]>=0.24.0
python-multipart
torch>=2.1.0
torchaudio>=2.1.0
numpy>=1.24.0
librosa>=0.10.0
soundfile>=0.12.0
```

## Implementação Fase 2: Processamento em Batch

### 1. Batch Processing Service

```python
# batch_processor.py
import asyncio
from typing import List, Dict
import time

class BatchProcessor:
    def __init__(self, model_pool, batch_size=4, max_wait_time=2.0):
        self.model_pool = model_pool
        self.batch_size = batch_size
        self.max_wait_time = max_wait_time
        self.pending_requests = []
        self.processing = False
        
    async def add_request(self, file_path: str, request_id: str):
        """Adiciona requisição ao batch"""
        future = asyncio.Future()
        self.pending_requests.append({
            'file_path': file_path,
            'request_id': request_id,
            'future': future,
            'timestamp': time.time()
        })
        
        # Inicia processamento se batch estiver cheio ou timeout
        if not self.processing:
            asyncio.create_task(self._process_batch())
            
        return await future
    
    async def _process_batch(self):
        """Processa batch de requisições"""
        if self.processing or not self.pending_requests:
            return
            
        self.processing = True
        
        try:
            # Aguarda batch ficar cheio ou timeout
            while len(self.pending_requests) < self.batch_size:
                await asyncio.sleep(0.1)
                oldest_request = min(self.pending_requests, key=lambda x: x['timestamp'])
                if time.time() - oldest_request['timestamp'] > self.max_wait_time:
                    break
            
            # Processa batch atual
            current_batch = self.pending_requests[:self.batch_size]
            self.pending_requests = self.pending_requests[self.batch_size:]
            
            await self._execute_batch(current_batch)
            
        finally:
            self.processing = False
            
            # Processa próximo batch se houver requisições pendentes
            if self.pending_requests:
                asyncio.create_task(self._process_batch())
    
    async def _execute_batch(self, batch: List[Dict]):
        """Executa transcrição em batch"""
        model = await self.model_pool.get_model()
        
        try:
            # Processa arquivos em paralelo
            tasks = []
            for request in batch:
                task = asyncio.create_task(
                    self._transcribe_single(model, request)
                )
                tasks.append(task)
            
            await asyncio.gather(*tasks, return_exceptions=True)
            
        finally:
            await self.model_pool.return_model(model)
    
    async def _transcribe_single(self, model, request):
        """Transcreve arquivo único"""
        try:
            loop = asyncio.get_event_loop()
            text = await loop.run_in_executor(
                None,
                self.model_pool.transcribe_sync,
                model,
                request['file_path']
            )
            request['future'].set_result(text)
        except Exception as e:
            request['future'].set_exception(e)
```

## Configuração Docker Otimizada

### docker-compose.optimized.yml

```yaml
version: '3.8'

services:
  whisper-optimized:
    build:
      context: .
      dockerfile: Dockerfile.whisper.optimized
    ports:
      - "11434:11434"
    volumes:
      - shared-data:/app/data
    restart: unless-stopped
    environment:
      - NVIDIA_VISIBLE_DEVICES=all
      - NVIDIA_DRIVER_CAPABILITIES=compute,utility
      - MODEL_SIZE=medium
      - COMPUTE_TYPE=int8
      - MAX_MODELS=12
      - BATCH_SIZE=8
    deploy:
      resources:
        reservations:
          devices:
            - driver: nvidia
              count: 1
              capabilities: [gpu]
        limits:
          memory: 16G
    runtime: nvidia
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:11434/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  transcribe-optimized:
    build:
      context: .
      dockerfile: Dockerfile.transcribe.optimized
    volumes:
      - shared-data:/app/data
    ports:
      - "8080:8080"
    environment:
      - WHISPER_SERVICE_URL=http://whisper-optimized:11434/transcribe
      - MAX_CONCURRENT_REQUESTS=100
      - REQUEST_TIMEOUT=300
    depends_on:
      - whisper-optimized

  nginx-optimized:
    image: nginx:latest
    ports:
      - "80:80"
    volumes:
      - ./nginx.optimized.conf:/etc/nginx/nginx.conf:ro
    depends_on:
      - transcribe-optimized

volumes:
  shared-data:
```

## Monitoramento e Métricas

### 1. Sistema de Métricas

```python
# metrics.py
import time
import asyncio
from collections import defaultdict, deque
import psutil
import GPUtil

class MetricsCollector:
    def __init__(self):
        self.request_times = deque(maxlen=1000)
        self.error_count = defaultdict(int)
        self.active_requests = 0
        self.total_requests = 0
        
    def record_request_start(self):
        self.active_requests += 1
        self.total_requests += 1
        return time.time()
    
    def record_request_end(self, start_time, success=True):
        self.active_requests -= 1
        duration = time.time() - start_time
        self.request_times.append(duration)
        
        if not success:
            self.error_count['transcription_error'] += 1
    
    def get_metrics(self):
        gpu = GPUtil.getGPUs()[0] if GPUtil.getGPUs() else None
        
        return {
            'active_requests': self.active_requests,
            'total_requests': self.total_requests,
            'avg_response_time': sum(self.request_times) / len(self.request_times) if self.request_times else 0,
            'requests_per_minute': len([t for t in self.request_times if time.time() - t < 60]),
            'error_rate': sum(self.error_count.values()) / max(self.total_requests, 1),
            'gpu_utilization': gpu.load * 100 if gpu else 0,
            'gpu_memory_used': gpu.memoryUsed if gpu else 0,
            'gpu_memory_total': gpu.memoryTotal if gpu else 0,
            'cpu_usage': psutil.cpu_percent(),
            'memory_usage': psutil.virtual_memory().percent
        }

# Integração com FastAPI
metrics = MetricsCollector()

@app.get("/metrics")
async def get_metrics():
    return metrics.get_metrics()
```

## Scripts de Teste de Performance

### 1. Teste de Carga Otimizado

```bash
#!/bin/bash
# test_optimized.sh

echo "Iniciando teste de carga otimizado..."

# Configurações
CONCURRENT_REQUESTS=50
TOTAL_FILES=200
SERVER_URL="http://localhost/transcribe"

# Função para enviar requisição
send_request() {
    local file=$1
    local id=$2
    
    start_time=$(date +%s.%N)
    
    response=$(curl -s -w "%{http_code}" -X POST \
        -F "audio=@$file" \
        "$SERVER_URL" \
        -o /tmp/response_$id.json)
    
    end_time=$(date +%s.%N)
    duration=$(echo "$end_time - $start_time" | bc)
    
    echo "Request $id: $response - ${duration}s"
}

# Executa testes em paralelo
export -f send_request
export SERVER_URL

find /path/to/audio/files -name "*.mp3" | head -n $TOTAL_FILES | \
    xargs -n1 -P$CONCURRENT_REQUESTS -I{} bash -c 'send_request "{}" $RANDOM'

echo "Teste concluído!"
```

## Resultados Esperados

### Performance Atual vs Otimizada

| Métrica | Atual | Otimizada | Melhoria |
|---------|-------|-----------|----------|
| **Modelos Simultâneos** | 5 | 12-16 | 2.4-3.2x |
| **Uso VRAM** | 20GB | 12-16GB | 25-40% redução |
| **Concorrência** | 5 req/s | 20-30 req/s | 4-6x |
| **Throughput** | 50-100 arq/h | 300-500 arq/h | 3-5x |
| **Latência Média** | 10-15s | 5-8s | 40-50% redução |
| **Tempo Inicialização** | 50s | 15-20s | 60% redução |

### Utilização de Recursos A10G

- **VRAM**: 70-80% (vs 85% atual)
- **GPU Compute**: 90-95% (vs 60% atual)
- **CPU**: 60-70% (vs 40% atual)
- **Throughput**: 400+ arquivos/hora sustentado
