# Documentação do Projeto Transcriber

## Visão Geral

O **Transcriber** é um sistema de transcrição de áudio baseado em microserviços que utiliza o modelo Whisper da OpenAI para converter arquivos de áudio em texto. O projeto é implementado usando Docker e oferece uma API REST para processamento de transcrições de áudio.

## Arquitetura

O sistema é composto por três serviços principais:

### 1. <PERSON><PERSON><PERSON><PERSON> Whisper (`whisper`)
- **Porta**: 11434
- **Função**: Processamento de transcrição usando modelos Whisper
- **Tecnologias**: Python, Flask, faster-whisper, CUDA
- **GPU**: Requer GPU NVIDIA com suporte CUDA

### 2. Serviço Transcribe (`transcribe`)
- **Porta**: 8080
- **Função**: API gateway que recebe arquivos de áudio e coordena com o serviço Whisper
- **Tecnologias**: Python, Flask, Gunicorn

### 3. <PERSON><PERSON><PERSON> (Load Balancer)
- **Porta**: 80
- **Função**: Proxy reverso e balanceamento de carga
- **Configuração**: Redireciona requisições para o serviço transcribe

## Estrutura de Arquivos

```
transcriber/
├── docker-compose.yml          # Orquestração dos serviços
├── Dockerfile                  # Container principal (transcribe)
├── Dockerfile.transcribe       # Container do serviço transcribe
├── Dockerfile.whisper          # Container do serviço whisper
├── transcribe.py              # API gateway principal
├── whisper.py                 # Serviço de transcrição Whisper
├── whisper_prod.py            # Versão de produção do Whisper
├── requirements.txt           # Dependências Python
├── gunicorn_config.py         # Configuração do Gunicorn
├── nginx.conf                 # Configuração do Nginx
├── upgrade_checkpoint.py      # Script de atualização de checkpoint
├── local-test.py              # Script de teste local
├── test.sh                    # Script de teste de carga
└── audio_time.sh              # Script para calcular duração de áudios
```

## Tecnologias Utilizadas

### Backend
- **Python 3.10+**
- **Flask**: Framework web para APIs REST
- **Gunicorn**: Servidor WSGI para produção
- **faster-whisper**: Implementação otimizada do Whisper
- **PyTorch**: Framework de machine learning
- **CUDA**: Aceleração GPU

### Infraestrutura
- **Docker & Docker Compose**: Containerização
- **Nginx**: Proxy reverso e load balancer
- **NVIDIA Container Runtime**: Suporte GPU em containers

### Dependências Python
```
flask
ffmpeg-python
gunicorn
uvicorn
requests
gevent
python-dotenv
```

## Funcionalidades

### 1. Transcrição de Áudio
- Suporte a múltiplos formatos: MP3, WAV, OGA
- Processamento em português (configurado como padrão)
- Modelo Whisper large-v3 para alta precisão
- Pool de 5 instâncias do modelo para processamento paralelo

### 2. Gerenciamento de Recursos
- Sistema de reserva de modelos com threading
- Fila de requisições para otimizar uso de GPU
- Retry automático em caso de falhas (até 3 tentativas)
- Limpeza automática de arquivos temporários

### 3. Monitoramento e Logs
- Logging detalhado em todos os serviços
- Tratamento de erros robusto
- Métricas de tempo de processamento

## API Endpoints

### POST /transcribe
Transcreve um arquivo de áudio para texto.

**Request:**
```bash
curl -X POST \
  -F "audio=@arquivo.mp3" \
  http://localhost/transcribe
```

**Response (Sucesso):**
```json
{
  "text": "Texto transcrito do áudio..."
}
```

**Response (Erro):**
```json
{
  "error": "Descrição do erro",
  "file_path": "/caminho/do/arquivo"
}
```

## Configuração e Deploy

### Pré-requisitos
- Docker e Docker Compose
- GPU NVIDIA com drivers CUDA 12.5+
- NVIDIA Container Runtime

### Variáveis de Ambiente
```bash
GUNICORN_WORKERS=4          # Número de workers Gunicorn
GUNICORN_THREADS=2          # Threads por worker
WHISPER_SERVICE_URL=http://whisper:11434/transcribe
```

### Execução
```bash
# Construir e iniciar todos os serviços
docker-compose up --build

# Executar em background
docker-compose up -d --build

# Parar serviços
docker-compose down
```

## Otimizações de Performance

### 1. Pool de Modelos
- 5 instâncias do modelo Whisper carregadas simultaneamente
- Sistema de reserva thread-safe para distribuição de carga
- Carregamento sequencial com delay para estabilidade

### 2. Configuração GPU
- Uso de float16 para reduzir uso de memória
- Batch size otimizado (16)
- Suporte completo CUDA com cuDNN

### 3. Servidor Web
- Gunicorn com workers gevent para I/O assíncrono
- Configuração automática baseada em CPU cores
- Nginx para balanceamento e cache

## Scripts de Teste

### test.sh
Script para teste de carga que:
- Processa até 50 arquivos simultaneamente
- Executa 15 requisições paralelas
- Mede tempo total de processamento
- Ordena arquivos por tamanho

### audio_time.sh
Calcula duração total de arquivos de áudio em um diretório.

## Monitoramento

### Logs
- Logs detalhados em cada serviço
- Rastreamento de reserva/liberação de modelos
- Métricas de tempo de processamento
- Tratamento de exceções com stack trace

### Métricas
- Tempo de transcrição por arquivo
- Status de modelos (livre/ocupado)
- Fila de requisições pendentes

## Análise de Performance para AWS G5 (NVIDIA A10G)

### Situação Atual - Problemas Identificados

#### 1. **Uso Ineficiente da GPU A10G (24GB VRAM)**
- **Problema**: Carregamento de 5 modelos large-v3 consome ~20GB VRAM
- **Impacto**: Apenas 5 transcrições simultâneas máximo
- **Modelo large-v3**: ~4GB VRAM por instância
- **Desperdício**: Subutilização da capacidade de processamento

#### 2. **Arquitetura de Threading Limitada**
- **Problema**: Python GIL limita paralelismo real
- **Impacto**: Threads competem por recursos, não há paralelismo verdadeiro
- **Bottleneck**: Sistema de reserva/liberação com locks

#### 3. **Estratégia de Carregamento Subótima**
- **Problema**: Carregamento sequencial com delay de 10s
- **Impacto**: Tempo de inicialização de 50+ segundos
- **Ineficiência**: Modelos idênticos carregados múltiplas vezes

### Otimizações Recomendadas para Alta Concorrência

#### **Estratégia 1: Otimização do Modelo e Memória**

**A. Usar Modelo Medium em vez de Large-v3**
- **Vantagem**: 2GB VRAM vs 4GB (50% menos memória)
- **Performance**: 95% da qualidade do large-v3 para português
- **Capacidade**: 10-12 instâncias simultâneas vs 5 atuais
- **Trade-off**: Perda mínima de qualidade vs 2x mais throughput

**B. Implementar Quantização INT8**
```python
model = WhisperModel("medium", device="cuda", compute_type="int8")
```
- **Benefício**: Reduz uso de VRAM em 50%
- **Resultado**: 16-20 instâncias simultâneas possíveis
- **Qualidade**: Perda mínima (<2%) na precisão

#### **Estratégia 2: Arquitetura Assíncrona com FastAPI**

**Migração de Flask para FastAPI + AsyncIO**
```python
import asyncio
from fastapi import FastAPI
from concurrent.futures import ThreadPoolExecutor

app = FastAPI()
executor = ThreadPoolExecutor(max_workers=20)

@app.post("/transcribe")
async def transcribe_async(audio: UploadFile):
    loop = asyncio.get_event_loop()
    result = await loop.run_in_executor(executor, transcribe_audio, file_path)
    return result
```

**Benefícios:**
- **Concorrência**: 100+ requisições simultâneas
- **Eficiência**: Não bloqueia threads durante I/O
- **Escalabilidade**: Melhor uso de recursos CPU

#### **Estratégia 3: Pool de Modelos Dinâmico**

```python
class DynamicModelPool:
    def __init__(self, max_models=16):
        self.max_models = max_models
        self.models = []
        self.available = asyncio.Queue()
        self.in_use = set()

    async def get_model(self):
        if len(self.models) < self.max_models and self.available.empty():
            model = self._load_model()
            self.models.append(model)
            await self.available.put(model)

        return await self.available.get()

    async def return_model(self, model):
        await self.available.put(model)
```

#### **Estratégia 4: Processamento em Batch**

```python
async def batch_transcribe(audio_files: List[str], batch_size=4):
    model = await model_pool.get_model()
    try:
        # Processa múltiplos arquivos em um único modelo
        results = model.transcribe_batch(audio_files, batch_size=batch_size)
        return results
    finally:
        await model_pool.return_model(model)
```

### Comparação de Linguagens para Alta Performance

#### **Python (Atual) vs Alternativas**

| Aspecto | Python | Go | Rust | Node.js |
|---------|--------|----|----- |---------|
| **Concorrência** | Limitada (GIL) | Excelente (goroutines) | Excelente (async) | Boa (event loop) |
| **Uso de Memória** | Alto | Baixo | Muito baixo | Médio |
| **Integração ML** | Nativa | Limitada | Crescente | Limitada |
| **Desenvolvimento** | Rápido | Médio | Lento | Rápido |
| **Performance I/O** | Baixa | Alta | Muito alta | Alta |

#### **Recomendação: Manter Python com Otimizações**

**Razões:**
1. **Integração Whisper**: Bibliotecas nativas em Python
2. **Desenvolvimento**: Equipe já familiarizada
3. **Ecossistema ML**: Melhor suporte para modelos
4. **Otimizações Possíveis**: AsyncIO resolve limitações de concorrência

### Arquitetura Otimizada Proposta

#### **Configuração Recomendada para A10G**

```yaml
# docker-compose.optimized.yml
services:
  whisper-pool:
    replicas: 2  # 2 containers para redundância
    environment:
      - MODEL_SIZE=medium
      - COMPUTE_TYPE=int8
      - MAX_MODELS_PER_CONTAINER=8
      - BATCH_SIZE=4
    deploy:
      resources:
        reservations:
          devices:
            - driver: nvidia
              count: 1
              capabilities: [gpu]
```

#### **Estimativa de Performance**

**Configuração Atual:**
- **Modelos**: 5x large-v3 (float16)
- **Concorrência**: 5 transcrições simultâneas
- **Throughput**: ~50-100 arquivos/hora

**Configuração Otimizada:**
- **Modelos**: 16x medium (int8)
- **Concorrência**: 50+ requisições simultâneas
- **Throughput**: ~300-500 arquivos/hora (3-5x melhoria)

### Implementação Gradual

#### **Fase 1: Otimizações Imediatas (1-2 dias)**
1. Migrar para modelo medium
2. Implementar quantização int8
3. Aumentar número de instâncias para 10-12

#### **Fase 2: Refatoração Assíncrona (1 semana)**
1. Migrar Flask para FastAPI
2. Implementar pool dinâmico de modelos
3. Adicionar processamento em batch

#### **Fase 3: Otimizações Avançadas (2 semanas)**
1. Implementar cache de resultados
2. Adicionar métricas detalhadas
3. Otimizar pipeline de pré-processamento

### Monitoramento de Performance

```python
# Métricas essenciais para monitorar
metrics = {
    "gpu_memory_usage": "nvidia-smi",
    "concurrent_requests": "active_transcriptions",
    "queue_length": "pending_requests",
    "avg_processing_time": "per_file_duration",
    "throughput": "files_per_hour",
    "error_rate": "failed_transcriptions"
}
```

## Considerações de Produção

### Recursos Necessários (Otimizado para A10G)
- **GPU**: NVIDIA A10G (24GB VRAM) - utilização 90%+
- **RAM**: 32GB (recomendado 64GB para cache)
- **CPU**: 16+ cores para workers assíncronos
- **Storage**: NVMe SSD para cache de modelos e arquivos temporários

### Escalabilidade
- **Horizontal**: Múltiplas instâncias com load balancer
- **Vertical**: 16+ modelos por GPU A10G
- **Multi-GPU**: Suporte a múltiplas A10G em paralelo

### Segurança
- Validação de tipos de arquivo
- Limpeza automática de arquivos temporários
- Tratamento seguro de exceções
- Logs sem exposição de dados sensíveis
- Rate limiting por IP/usuário
