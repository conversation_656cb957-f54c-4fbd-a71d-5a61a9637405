# Documentação do Projeto Transcriber

## Visão Geral

O **Transcriber** é um sistema de transcrição de áudio baseado em microserviços que utiliza o modelo Whisper da OpenAI para converter arquivos de áudio em texto. O projeto é implementado usando Docker e oferece uma API REST para processamento de transcrições de áudio.

## Arquitetura

O sistema é composto por três serviços principais:

### 1. <PERSON><PERSON><PERSON><PERSON> Whisper (`whisper`)
- **Porta**: 11434
- **Função**: Processamento de transcrição usando modelos Whisper
- **Tecnologias**: Python, Flask, faster-whisper, CUDA
- **GPU**: Requer GPU NVIDIA com suporte CUDA

### 2. Serviço Transcribe (`transcribe`)
- **Porta**: 8080
- **Função**: API gateway que recebe arquivos de áudio e coordena com o serviço Whisper
- **Tecnologias**: Python, Flask, Gunicorn

### 3. <PERSON><PERSON><PERSON> (Load Balancer)
- **Porta**: 80
- **Função**: Proxy reverso e balanceamento de carga
- **Configuração**: Redireciona requisições para o serviço transcribe

## Estrutura de Arquivos

```
transcriber/
├── docker-compose.yml          # Orquestração dos serviços
├── Dockerfile                  # Container principal (transcribe)
├── Dockerfile.transcribe       # Container do serviço transcribe
├── Dockerfile.whisper          # Container do serviço whisper
├── transcribe.py              # API gateway principal
├── whisper.py                 # Serviço de transcrição Whisper
├── whisper_prod.py            # Versão de produção do Whisper
├── requirements.txt           # Dependências Python
├── gunicorn_config.py         # Configuração do Gunicorn
├── nginx.conf                 # Configuração do Nginx
├── upgrade_checkpoint.py      # Script de atualização de checkpoint
├── local-test.py              # Script de teste local
├── test.sh                    # Script de teste de carga
└── audio_time.sh              # Script para calcular duração de áudios
```

## Tecnologias Utilizadas

### Backend
- **Python 3.10+**
- **Flask**: Framework web para APIs REST
- **Gunicorn**: Servidor WSGI para produção
- **faster-whisper**: Implementação otimizada do Whisper
- **PyTorch**: Framework de machine learning
- **CUDA**: Aceleração GPU

### Infraestrutura
- **Docker & Docker Compose**: Containerização
- **Nginx**: Proxy reverso e load balancer
- **NVIDIA Container Runtime**: Suporte GPU em containers

### Dependências Python
```
flask
ffmpeg-python
gunicorn
uvicorn
requests
gevent
python-dotenv
```

## Funcionalidades

### 1. Transcrição de Áudio
- Suporte a múltiplos formatos: MP3, WAV, OGA
- Processamento em português (configurado como padrão)
- Modelo Whisper large-v3 para alta precisão
- Pool de 5 instâncias do modelo para processamento paralelo

### 2. Gerenciamento de Recursos
- Sistema de reserva de modelos com threading
- Fila de requisições para otimizar uso de GPU
- Retry automático em caso de falhas (até 3 tentativas)
- Limpeza automática de arquivos temporários

### 3. Monitoramento e Logs
- Logging detalhado em todos os serviços
- Tratamento de erros robusto
- Métricas de tempo de processamento

## API Endpoints

### POST /transcribe
Transcreve um arquivo de áudio para texto.

**Request:**
```bash
curl -X POST \
  -F "audio=@arquivo.mp3" \
  http://localhost/transcribe
```

**Response (Sucesso):**
```json
{
  "text": "Texto transcrito do áudio..."
}
```

**Response (Erro):**
```json
{
  "error": "Descrição do erro",
  "file_path": "/caminho/do/arquivo"
}
```

## Configuração e Deploy

### Pré-requisitos
- Docker e Docker Compose
- GPU NVIDIA com drivers CUDA 12.5+
- NVIDIA Container Runtime

### Variáveis de Ambiente
```bash
GUNICORN_WORKERS=4          # Número de workers Gunicorn
GUNICORN_THREADS=2          # Threads por worker
WHISPER_SERVICE_URL=http://whisper:11434/transcribe
```

### Execução
```bash
# Construir e iniciar todos os serviços
docker-compose up --build

# Executar em background
docker-compose up -d --build

# Parar serviços
docker-compose down
```

## Otimizações de Performance

### 1. Pool de Modelos
- 5 instâncias do modelo Whisper carregadas simultaneamente
- Sistema de reserva thread-safe para distribuição de carga
- Carregamento sequencial com delay para estabilidade

### 2. Configuração GPU
- Uso de float16 para reduzir uso de memória
- Batch size otimizado (16)
- Suporte completo CUDA com cuDNN

### 3. Servidor Web
- Gunicorn com workers gevent para I/O assíncrono
- Configuração automática baseada em CPU cores
- Nginx para balanceamento e cache

## Scripts de Teste

### test.sh
Script para teste de carga que:
- Processa até 50 arquivos simultaneamente
- Executa 15 requisições paralelas
- Mede tempo total de processamento
- Ordena arquivos por tamanho

### audio_time.sh
Calcula duração total de arquivos de áudio em um diretório.

## Monitoramento

### Logs
- Logs detalhados em cada serviço
- Rastreamento de reserva/liberação de modelos
- Métricas de tempo de processamento
- Tratamento de exceções com stack trace

### Métricas
- Tempo de transcrição por arquivo
- Status de modelos (livre/ocupado)
- Fila de requisições pendentes

## Considerações de Produção

### Recursos Necessários
- **GPU**: NVIDIA com pelo menos 8GB VRAM
- **RAM**: Mínimo 16GB (recomendado 32GB)
- **CPU**: Multi-core para workers Gunicorn
- **Storage**: SSD para cache de modelos

### Escalabilidade
- Horizontal: Múltiplas instâncias do serviço whisper
- Vertical: Aumento de workers e threads
- GPU: Suporte a múltiplas GPUs

### Segurança
- Validação de tipos de arquivo
- Limpeza automática de arquivos temporários
- Tratamento seguro de exceções
- Logs sem exposição de dados sensíveis
