# Especificações Técnicas: Nova Arquitetura API

## 🎯 Visão Geral da Arquitetura

```
┌─────────────┐    ┌─────────────┐    ┌─────────────┐    ┌─────────────┐
│   Nginx     │───▶│   Go API    │───▶│ Go Producer │───▶│ RabbitMQ    │
│ (Port 80)   │    │ (Port 8080) │    │ (Internal)  │    │ (Port 5672) │
└─────────────┘    └─────────────┘    └─────────────┘    └─────────────┘
                          │                                      │
                          ▼                                      ▼
                   ┌─────────────┐                        ┌─────────────┐
                   │    Redis    │                        │   Python    │
                   │ (Port 6379) │                        │  Consumer   │
                   └─────────────┘                        │ (Whisper)   │
                                                          └─────────────┘
```

## 📡 API Specifications

### **Go API Service (Port 8080)**

#### **POST /transcribe**
Upload de arquivo de áudio para transcrição

**Request**:
```http
POST /transcribe HTTP/1.1
Content-Type: multipart/form-data

--boundary
Content-Disposition: form-data; name="audio"; filename="audio.mp3"
Content-Type: audio/mpeg

[binary audio data]
--boundary--
```

**Response Success (202 Accepted)**:
```json
{
  "task_id": "550e8400-e29b-41d4-a716-************",
  "status": "uploaded",
  "upload_time": "2024-01-01T10:00:00Z",
  "estimated_wait": "30 seconds",
  "file_size": 1048576,
  "message": "File uploaded successfully, processing started"
}
```

**Response Error (400 Bad Request)**:
```json
{
  "error": "invalid_file",
  "message": "File format not supported. Supported: mp3, wav, oga",
  "supported_formats": ["mp3", "wav", "oga"],
  "max_file_size": 104857600
}
```

#### **GET /status/{task_id}**
Consultar status de uma transcrição

**Response - Processing**:
```json
{
  "task_id": "550e8400-e29b-41d4-a716-************",
  "status": "processing",
  "created_at": "2024-01-01T10:00:00Z",
  "started_at": "2024-01-01T10:00:30Z",
  "estimated_completion": "2024-01-01T10:01:00Z",
  "worker_id": "consumer-1",
  "queue_position": null
}
```

**Response - Completed**:
```json
{
  "task_id": "550e8400-e29b-41d4-a716-************",
  "status": "completed",
  "result": "Este é o texto transcrito do áudio enviado.",
  "created_at": "2024-01-01T10:00:00Z",
  "completed_at": "2024-01-01T10:01:15Z",
  "processing_time": 45.2,
  "worker_id": "consumer-1"
}
```

**Response - Failed**:
```json
{
  "task_id": "550e8400-e29b-41d4-a716-************",
  "status": "failed",
  "error": "transcription_failed",
  "message": "Audio file corrupted or unsupported format",
  "created_at": "2024-01-01T10:00:00Z",
  "failed_at": "2024-01-01T10:00:45Z",
  "retries": 3
}
```

#### **GET /health**
Health check do serviço

**Response**:
```json
{
  "status": "healthy",
  "timestamp": "2024-01-01T10:00:00Z",
  "version": "2.0.0",
  "uptime": 3600,
  "dependencies": {
    "redis": "connected",
    "storage": "available",
    "producer": "healthy"
  },
  "metrics": {
    "active_uploads": 5,
    "total_requests": 1250,
    "avg_response_time": 85.5
  }
}
```

#### **GET /metrics**
Métricas detalhadas (Prometheus format)

**Response**:
```
# HELP transcriber_requests_total Total number of requests
# TYPE transcriber_requests_total counter
transcriber_requests_total{status="success"} 1200
transcriber_requests_total{status="error"} 50

# HELP transcriber_request_duration_seconds Request duration
# TYPE transcriber_request_duration_seconds histogram
transcriber_request_duration_seconds_bucket{le="0.1"} 800
transcriber_request_duration_seconds_bucket{le="0.5"} 1150
transcriber_request_duration_seconds_bucket{le="1.0"} 1200
```

## 🔄 RabbitMQ Message Specifications

### **Queue: transcription_queue**

**Message Format**:
```json
{
  "task_id": "550e8400-e29b-41d4-a716-************",
  "file_path": "/app/data/550e8400-e29b-41d4-a716-************_audio.mp3",
  "file_size": 1048576,
  "file_hash": "sha256:a665a45920422f9d417e4867efdc4fb8a04a1f3fff1fa07e998e86f7f7a27ae3",
  "created_at": "2024-01-01T10:00:00Z",
  "priority": 5,
  "retries": 0,
  "metadata": {
    "original_filename": "audio.mp3",
    "content_type": "audio/mpeg",
    "source_ip": "*************",
    "user_agent": "curl/7.68.0"
  }
}
```

### **Queue Configuration**:
```yaml
transcription_queue:
  durable: true
  auto_delete: false
  arguments:
    x-message-ttl: 3600000        # 1 hora
    x-max-length: 1000            # Máximo 1000 mensagens
    x-dead-letter-exchange: "dlx"
    x-dead-letter-routing-key: "failed"

retry_queue:
  durable: true
  auto_delete: false
  arguments:
    x-message-ttl: 30000          # 30 segundos
    x-dead-letter-exchange: "main"
    x-dead-letter-routing-key: "transcription"

failed_queue:
  durable: true
  auto_delete: false
  # Mensagens que falharam após todos os retries
```

## 🗄️ Redis Data Structures

### **Task Metadata: `task:{task_id}`**
```redis
HSET task:550e8400-e29b-41d4-a716-************
  status "processing"
  file_path "/app/data/550e8400-e29b-41d4-a716-************_audio.mp3"
  file_size "1048576"
  file_hash "sha256:a665a45920422f9d417e4867efdc4fb8a04a1f3fff1fa07e998e86f7f7a27ae3"
  created_at "2024-01-01T10:00:00Z"
  started_at "2024-01-01T10:00:30Z"
  worker_id "consumer-1"
  original_filename "audio.mp3"
  source_ip "*************"

# TTL: 24 horas
EXPIRE task:550e8400-e29b-41d4-a716-************ 86400
```

### **System Metrics: `metrics:*`**
```redis
# Contadores globais
INCR metrics:requests:total
INCR metrics:requests:success
INCR metrics:requests:error

# Métricas por hora
INCR metrics:hourly:2024-01-01:10
EXPIRE metrics:hourly:2024-01-01:10 604800  # 7 dias

# Queue size tracking
SET metrics:queue:size 25
SET metrics:active:consumers 3
```

### **Cache de Resultados: `result:{file_hash}`**
```redis
# Cache baseado em hash do arquivo (deduplicação)
SET result:sha256:a665a45920422f9d417e4867efdc4fb8a04a1f3fff1fa07e998e86f7f7a27ae3
  "Este é o texto transcrito do áudio."
EXPIRE result:sha256:a665a45920422f9d417e4867efdc4fb8a04a1f3fff1fa07e998e86f7f7a27ae3 2592000  # 30 dias
```

## 🐳 Docker Configuration

### **docker-compose.yml**
```yaml
version: '3.8'

services:
  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf:ro
    depends_on:
      - go-api

  go-api:
    build: ./go-api
    ports:
      - "8080:8080"
    environment:
      - REDIS_URL=redis:6379
      - STORAGE_PATH=/app/data
      - MAX_FILE_SIZE=104857600
      - PRODUCER_URL=http://go-producer:8081
    volumes:
      - shared-data:/app/data
    depends_on:
      - redis
      - go-producer

  go-producer:
    build: ./go-producer
    ports:
      - "8081:8081"
    environment:
      - REDIS_URL=redis:6379
      - RABBITMQ_URL=amqp://admin:admin123@rabbitmq:5672/
      - STORAGE_PATH=/app/data
      - MAX_QUEUE_SIZE=50
      - MAX_ACTIVE_JOBS=8
    volumes:
      - shared-data:/app/data
    depends_on:
      - redis
      - rabbitmq

  python-consumer:
    build: ./python-consumer
    environment:
      - REDIS_URL=redis:6379
      - RABBITMQ_URL=amqp://admin:admin123@rabbitmq:5672/
      - WORKER_ID=consumer-1
      - MODEL_SIZE=medium
      - COMPUTE_TYPE=int8
      - MAX_CONCURRENT=8
    volumes:
      - shared-data:/app/data
    deploy:
      resources:
        reservations:
          devices:
            - driver: nvidia
              count: 1
              capabilities: [gpu]
    runtime: nvidia
    depends_on:
      - redis
      - rabbitmq

  rabbitmq:
    image: rabbitmq:3-management
    ports:
      - "5672:5672"
      - "15672:15672"
    environment:
      - RABBITMQ_DEFAULT_USER=admin
      - RABBITMQ_DEFAULT_PASS=admin123
    volumes:
      - rabbitmq-data:/var/lib/rabbitmq

  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    volumes:
      - redis-data:/data
    command: redis-server --appendonly yes --maxmemory 2gb --maxmemory-policy allkeys-lru

volumes:
  shared-data:
  rabbitmq-data:
  redis-data:
```

## 🔧 Environment Variables

### **Go API Service**
```bash
# Server
PORT=8080
GIN_MODE=release

# Redis
REDIS_URL=redis:6379
REDIS_PASSWORD=""
REDIS_DB=0

# Storage
STORAGE_PATH=/app/data
MAX_FILE_SIZE=104857600  # 100MB
CLEANUP_INTERVAL=3600    # 1 hora

# Producer
PRODUCER_URL=http://go-producer:8081
PRODUCER_TIMEOUT=30s

# Logging
LOG_LEVEL=info
LOG_FORMAT=json
```

### **Go Producer Service**
```bash
# Server
PORT=8081

# Redis
REDIS_URL=redis:6379

# RabbitMQ
RABBITMQ_URL=amqp://admin:admin123@rabbitmq:5672/
RABBITMQ_QUEUE=transcription_queue
RABBITMQ_EXCHANGE=transcription

# Flow Control
MAX_QUEUE_SIZE=50
MAX_ACTIVE_JOBS=8
THROTTLE_CHECK_INTERVAL=5s

# File Validation
SUPPORTED_FORMATS=mp3,wav,oga,m4a
MAX_FILE_SIZE=104857600
FILE_READY_CHECK_INTERVAL=100ms
```

### **Python Consumer Service**
```bash
# Worker
WORKER_ID=consumer-1
MAX_CONCURRENT=8

# Redis
REDIS_URL=redis:6379

# RabbitMQ
RABBITMQ_URL=amqp://admin:admin123@rabbitmq:5672/
RABBITMQ_QUEUE=transcription_queue
PREFETCH_COUNT=8

# Whisper
MODEL_SIZE=medium
COMPUTE_TYPE=int8
DEVICE=cuda
LANGUAGE=pt
BATCH_SIZE=16

# Performance
VAD_FILTER=true
BEAM_SIZE=1
BEST_OF=1
TEMPERATURE=0.0
```

## 📊 Performance Targets

### **Latency**
- API Response: <100ms
- File Upload: <500ms (10MB file)
- Queue Processing: <2s (from upload to queue)
- Transcription: 8-12s (30s audio file)

### **Throughput**
- API Requests: 100+ req/s
- File Uploads: 50+ simultaneous
- Transcriptions: 400+ files/hour
- Queue Processing: 1000+ msg/s

### **Resource Usage**
- Go API: <100MB RAM, <10% CPU
- Go Producer: <200MB RAM, <20% CPU  
- Python Consumer: <4GB RAM, 90% GPU
- Redis: <2GB RAM
- RabbitMQ: <1GB RAM

### **Reliability**
- Uptime: 99.9%+
- Error Rate: <1%
- Message Loss: 0%
- Recovery Time: <30s
