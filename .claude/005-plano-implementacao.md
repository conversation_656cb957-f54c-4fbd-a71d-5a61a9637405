# Plano de Implementação: Nova Arquitetura API

## 🎯 Objetivo
Migrar do sistema atual (Flask monolítico) para arquitetura híbrida (Go + Python + RabbitMQ) com capacidade de processar 50+ arquivos simultâneos sem derrubar o Whisper.

## 📋 Cronograma Geral

### **Semana 1: Fundação e Planejamento**
- ✅ Análise e especificações técnicas
- ✅ Setup do ambiente de desenvolvimento
- ✅ Estrutura base do projeto

### **Semana 2-3: Desenvolvimento Core**
- 🔄 Go API Service (upload rápido)
- 🔄 Go Producer Service (validação + fila)
- 🔄 Infraestrutura RabbitMQ + Redis

### **Semana 4: Otimização Python**
- 🔄 Refatoração Python Consumer
- 🔄 Integração com RabbitMQ
- 🔄 Pool de modelos otimizado

### **Semana 5: Integração e Testes**
- 🔄 Testes de integração
- 🔄 Testes de performance (50+ arquivos)
- 🔄 Monitoramento e métricas

### **Semana 6: Deploy e Migração**
- 🔄 Deploy blue-green
- 🔄 Migração gradual
- 🔄 Monitoramento produção

## 📊 Tasks Detalhadas

### **FASE 1: Análise e Planejamento (3-5 dias)**

#### **Task 1.1: Definir Especificações Técnicas**
**Objetivo**: Documentar todas as interfaces e contratos entre serviços

**Entregáveis**:
- [ ] Schema de APIs (OpenAPI/Swagger)
- [ ] Formato de mensagens RabbitMQ
- [ ] Estrutura de dados Redis
- [ ] Configurações de ambiente

**Especificações**:
```yaml
# API Endpoints
POST /transcribe
  - Input: multipart/form-data (audio file)
  - Output: {"task_id": "uuid", "status": "uploaded", "estimated_wait": "30s"}
  
GET /status/{task_id}
  - Output: {"task_id": "uuid", "status": "completed", "result": "texto..."}

# RabbitMQ Message Format
{
  "task_id": "uuid",
  "file_path": "/app/data/uuid_filename.mp3",
  "file_size": 1048576,
  "created_at": "2024-01-01T10:00:00Z",
  "retries": 0
}

# Redis Keys Structure
task:{task_id} -> Hash {
  "status": "uploaded|processing|completed|failed",
  "file_path": "/app/data/...",
  "result": "texto transcrito...",
  "created_at": "timestamp",
  "completed_at": "timestamp"
}
```

#### **Task 1.2: Criar Estrutura de Projeto**
**Objetivo**: Organizar código e configurações

**Estrutura**:
```
transcriber-v2/
├── go-api/                 # Go API Service
│   ├── main.go
│   ├── handlers/
│   ├── models/
│   └── Dockerfile
├── go-producer/            # Go Producer Service  
│   ├── main.go
│   ├── validator/
│   ├── queue/
│   └── Dockerfile
├── python-consumer/        # Python Consumer Service
│   ├── consumer.py
│   ├── whisper_pool.py
│   ├── requirements.txt
│   └── Dockerfile
├── infrastructure/         # Docker Compose + Configs
│   ├── docker-compose.yml
│   ├── nginx.conf
│   ├── rabbitmq/
│   └── redis/
└── docs/                   # Documentação
    ├── api-spec.yaml
    └── deployment.md
```

#### **Task 1.3: Configurar Ambiente de Desenvolvimento**
**Objetivo**: Setup completo para desenvolvimento

**Checklist**:
- [ ] Go 1.21+ instalado
- [ ] Python 3.10+ com venv
- [ ] Docker + Docker Compose
- [ ] VS Code com extensões Go/Python
- [ ] Git hooks para linting
- [ ] Makefile para automação

### **FASE 2: Go API Service (5-7 dias)**

#### **Task 2.1: Desenvolver Handlers HTTP**
**Objetivo**: Endpoints básicos funcionando

**Implementação**:
```go
// main.go
package main

import (
    "github.com/gin-gonic/gin"
    "github.com/go-redis/redis/v8"
)

type APIService struct {
    redis   *redis.Client
    storage string
}

func (api *APIService) handleTranscribe(c *gin.Context) {
    // 1. Validação básica do arquivo
    // 2. Upload seguro com hash verification
    // 3. Salvar metadata no Redis
    // 4. Resposta imediata com task_id
    // 5. Trigger producer assíncrono
}

func (api *APIService) handleStatus(c *gin.Context) {
    // 1. Buscar status no Redis
    // 2. Retornar informações da task
}
```

**Critérios de Aceite**:
- [ ] Upload de arquivo em <100ms
- [ ] Validação de formato (mp3, wav, oga)
- [ ] Limite de tamanho (100MB)
- [ ] Resposta JSON estruturada
- [ ] Error handling robusto

#### **Task 2.2: Implementar Sistema de Upload Seguro**
**Objetivo**: Upload com integridade garantida

**Features**:
- [ ] Hash verification (SHA256)
- [ ] Operações atômicas (temp → final)
- [ ] Cleanup automático em caso de erro
- [ ] Prevenção de race conditions

#### **Task 2.3: Integrar Redis para Tracking**
**Objetivo**: Persistência de metadata e status

**Implementação**:
```go
func (api *APIService) storeTaskMetadata(taskID string, metadata map[string]interface{}) {
    api.redis.HMSet(ctx, "task:"+taskID, metadata)
    api.redis.Expire(ctx, "task:"+taskID, 24*time.Hour)
}

func (api *APIService) getTaskStatus(taskID string) (map[string]string, error) {
    return api.redis.HGetAll(ctx, "task:"+taskID).Result()
}
```

### **FASE 3: Go Producer Service (5-7 dias)**

#### **Task 3.1: Implementar Validação Completa de Arquivos**
**Objetivo**: Validação robusta antes do enfileiramento

**Validações**:
- [ ] Arquivo completamente gravado (timestamp check)
- [ ] Formato de áudio válido (ffprobe)
- [ ] Integridade (hash verification)
- [ ] Tamanho dentro dos limites

#### **Task 3.2: Desenvolver Sistema de Enfileiramento**
**Objetivo**: Integração com RabbitMQ

**Features**:
- [ ] Conexão persistente com RabbitMQ
- [ ] Serialização eficiente (msgpack)
- [ ] Retry automático em falhas
- [ ] Dead letter queue para falhas permanentes

#### **Task 3.3: Implementar Controle de Fluxo Inteligente**
**Objetivo**: Proteger Whisper de sobrecarga

**Algoritmo**:
```go
func (p *Producer) shouldThrottle() bool {
    activeJobs := p.getActiveConsumerJobs()
    queueSize := p.getQueueSize()
    
    // Limites de proteção
    maxActiveJobs := 8   // Número de modelos Whisper
    maxQueueSize := 50   // Limite da fila
    
    return activeJobs >= maxActiveJobs || queueSize >= maxQueueSize
}
```

### **FASE 4: Python Consumer Otimizado (5-7 dias)**

#### **Task 4.1: Refatorar Pool de Modelos Whisper**
**Objetivo**: Pool thread-safe e eficiente

**Implementação**:
```python
class OptimizedModelPool:
    def __init__(self, model_size="medium", compute_type="int8", max_models=8):
        self.models = []
        self.available = asyncio.Queue()
        self.stats = ModelStats()
        
    def initialize(self):
        for i in range(self.max_models):
            model = WhisperModel(
                self.model_size,
                device="cuda",
                compute_type=self.compute_type,
                cpu_threads=1,
                num_workers=1
            )
            self.models.append(model)
```

#### **Task 4.2: Implementar Consumer RabbitMQ**
**Objetivo**: Consumo eficiente da fila

**Features**:
- [ ] Conexão persistente
- [ ] QoS para controle de concorrência
- [ ] ACK/NACK inteligente
- [ ] Retry automático

#### **Task 4.3: Otimizar Processamento Whisper**
**Objetivo**: Configurações otimizadas para A10G

**Otimizações**:
- [ ] Modelo medium + int8 quantization
- [ ] Batch size otimizado (16)
- [ ] VAD filtering habilitado
- [ ] Beam size = 1 (mais rápido)

### **FASE 5: Infraestrutura e Integração (3-5 dias)**

#### **Task 5.1: Configurar RabbitMQ**
**Objetivo**: Filas otimizadas e confiáveis

**Configuração**:
```yaml
# Fila principal com FIFO
transcription_queue:
  durable: true
  arguments:
    x-message-ttl: 3600000  # 1 hora
    x-dead-letter-exchange: "dlx"

# Fila de retry
retry_queue:
  durable: true
  arguments:
    x-message-ttl: 30000    # 30 segundos
    x-dead-letter-exchange: "main"
```

#### **Task 5.2: Configurar Redis**
**Objetivo**: Cache e tracking otimizados

**Configuração**:
- [ ] Persistência habilitada
- [ ] Eviction policy: allkeys-lru
- [ ] Maxmemory: 2GB
- [ ] Clustering (se necessário)

### **FASE 6: Testes e Deploy (5-7 dias)**

#### **Task 6.1: Testes de Performance**
**Objetivo**: Validar capacidade de 50+ arquivos simultâneos

**Cenários de Teste**:
- [ ] 10 arquivos simultâneos
- [ ] 50 arquivos simultâneos  
- [ ] 100 arquivos simultâneos
- [ ] Teste de stress (500+ arquivos)

#### **Task 6.2: Deploy Blue-Green**
**Objetivo**: Deploy sem downtime

**Estratégia**:
- [ ] Ambiente blue (atual) mantido
- [ ] Ambiente green (novo) deployado
- [ ] Migração gradual de tráfego
- [ ] Rollback automático se necessário

## 🎯 Critérios de Sucesso

### **Performance**
- [ ] Throughput: 400+ arquivos/hora
- [ ] Latência API: <100ms resposta
- [ ] Concorrência: 50+ arquivos simultâneos
- [ ] Uptime: 99.9%+

### **Qualidade**
- [ ] Zero perda de arquivos
- [ ] Precisão mantida (vs sistema atual)
- [ ] Error rate: <1%
- [ ] Recovery automático de falhas

### **Operacional**
- [ ] Monitoramento completo
- [ ] Logs estruturados
- [ ] Alertas configurados
- [ ] Documentação atualizada

## 🚀 Próximos Passos Imediatos

1. **Hoje**: Finalizar especificações técnicas
2. **Amanhã**: Setup ambiente de desenvolvimento
3. **Esta semana**: Iniciar Go API Service
4. **Próxima semana**: Go Producer + RabbitMQ

**Estimativa total**: 4-6 semanas para implementação completa
