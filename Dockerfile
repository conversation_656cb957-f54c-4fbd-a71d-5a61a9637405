FROM nvidia/cuda:12.5.1-cudnn-devel-ubuntu22.04

WORKDIR /app

RUN apt-get update && apt-get install -y \
    libsndfile1 \
    ffmpeg \
    git \
    python3 \
    libcudnn8 \
    libcudnn8-dev \
    python3-pip \
    && rm -rf /var/lib/apt/lists/*

RUN python3 -m pip install --upgrade pip

RUN pip install torch torchvision torchaudio --index-url https://download.pytorch.org/whl/cu124

RUN pip install nvidia-pyindex
RUN pip install nvidia-cudnn

RUN pip install --force-reinstall "faster-whisper @ https://github.com/SYSTRAN/faster-whisper/archive/refs/heads/master.tar.gz"

COPY transcribe.py /app/transcribe.py
COPY requirements.txt /app/requirements.txt
COPY gunicorn_config.py /app/gunicorn_config.py
COPY upgrade_checkpoint.py /app/upgrade_checkpoint.py

RUN pip install -r requirements.txt

RUN python3 upgrade_checkpoint.py

EXPOSE 8080

CMD ["gunicorn", "-c", "gunicorn_config.py", "transcribe:app"]
