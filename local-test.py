ubuntu@VM-0-14-ubuntu:~/cervan$ cat instances.py 
from faster_whisper import WhisperModel, BatchedInferencePipeline
from flask import Flask, request, jsonify
import time
import json
import threading
from collections import deque

app = Flask(__name__)

# Cria três instâncias do modelo
model_instances = [
    BatchedInferencePipeline(model=WhisperModel("medium", device="cuda", compute_type="float16")),
    BatchedInferencePipeline(model=WhisperModel("medium", device="cuda", compute_type="float16")),
    BatchedInferencePipeline(model=WhisperModel("medium", device="cuda", compute_type="float16"))
]

model_states = [False, False, False]  # False indica que a instância está livre
model_lock = threading.Lock()
reservation_queue = deque()

def reserve_model():
    global model_states
    reservation_event = threading.Event()
    with model_lock:
        for i, state in enumerate(model_states):
            if not state:
                model_states[i] = True
                return i
        reservation_queue.append(reservation_event)
    reservation_event.wait()  # Espera até que um modelo esteja disponível
    return reserve_model()  # Tenta novamente para pegar um modelo agora que a fila está liberada

def release_model(index):
    global model_states
    with model_lock:
        model_states[index] = False
        if reservation_queue:
            reservation_event = reservation_queue.popleft()
            reservation_event.set()  # Avisa a próxima requisição na fila

def transcribe_audio(file_path):
    model_index = reserve_model()
    if model_index is None:
        return None, "No available model instances."

    try:
        model = model_instances[model_index]
        segments, info = model.transcribe(file_path, batch_size=16)
        text = "".join([segment.text for segment in segments])
        return text, info.language, info.language_probability
    finally:
        release_model(model_index)


@app.route('/transcribe', methods=['POST'])
def transcribe_endpoint():
    if 'audio' not in request.files:
        return jsonify({"error": "No audio file provided"}), 400

    audio_file = request.files['audio']
    file_path = f"/tmp/{audio_file.filename}"
    audio_file.save(file_path)

    start_time = time.time()
    text, language, language_probability = transcribe_audio(file_path)
    end_time = time.time()
    execution_time = end_time - start_time

    if text is None:
        return jsonify({"error": language}), 503

    print(f"Execution time: {execution_time} seconds")

    response = jsonify({"timing": execution_time, "text": text})
    response.set_data(json.dumps({"timing": execution_time, "text": text}, ensure_ascii=False))
    return response

if __name__ == "__main__":
    app.run(host='0.0.0.0', port=8095)