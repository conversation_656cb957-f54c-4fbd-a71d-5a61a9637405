import multiprocessing
import os
import math

cpu_count = multiprocessing.cpu_count()

def get_env_variable(var_name, default_value):
    value = os.getenv(var_name, default_value)
    try:
        return int(value)
    except (TypeError, ValueError):
        return default_value

workers = get_env_variable('GUNICORN_WORKERS', math.ceil(cpu_count * 0.75))
threads = get_env_variable('GUNICORN_THREADS', 2)

bind = "0.0.0.0:8080"
worker_class = "gunicorn.workers.ggevent.GeventWorker"
loglevel = "debug"  # Aumenta o nível de logging para debug

